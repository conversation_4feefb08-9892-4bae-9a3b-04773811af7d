import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { fal } from '@fal-ai/client';
import { GenVideoRequestDto } from '../dto/gen-video.dto';
import { GenPicRequestDto } from '../../lego/dto/gen-pic.dto';
import { negativePrompt } from '../constants';

@Injectable()
export class FalAiService {
    private readonly logger = new Logger(FalAiService.name);
    private readonly webhookUrl: string;
    private readonly picWebhookUrl: string;

    constructor(private configService: ConfigService) {
        const apiKey = this.configService.get<string>('FAL_AI_API_KEY');
        if (!apiKey) {
            this.logger.error('FAL_AI_API_KEY not found in environment variables');
        }

        // 初始化fal.ai客户端
        fal.config({
            credentials: apiKey
        });

        // 获取webhook基础URL
        const webhookBaseUrl = this.configService.get<string>('FAL_AI_WEBHOOK_BASE_URL');
        if (!webhookBaseUrl) {
            this.logger.error('FAL_AI_WEBHOOK_BASE_URL not found in environment variables');
        }
        this.webhookUrl = `${webhookBaseUrl}/generation/webhook/fal-ai`;
        this.picWebhookUrl = `${webhookBaseUrl}/lego/webhook/fal-ai`;
    }

    /**
     * 调用fal.ai的图片到视频API
     * @param modelId 模型ID
     * @param params 请求参数
     * @param taskId 本地任务ID，用于构建webhook URL
     * @returns 响应结果
     */
    async generateVideo(falModelName: string, params: GenVideoRequestDto): Promise<any> {
        if (falModelName === 'fal-ai/veo2' || falModelName === 'fal-ai/veo2/image-to-video') {
            // 为了适配傻逼veo2的特殊字段要求
            // @ts-ignore
            params.duration += 's';
        }
        try {
            const response = await fal.queue.submit(falModelName, {
                input: {
                    prompt: params.prompt,
                    negative_prompt: params.negative_prompt || negativePrompt,
                    cfg_scale: params.guidance_scale || 0.5,
                    duration: params.duration,
                    aspect_ratio: params.ratio,
                    image_url: params.refer_img_url || params.start_image_url,
                    // 首尾帧
                    start_image_url: params.start_image_url || params.refer_img_url,
                    end_image_url: params.end_image_url,
                    // kling的首尾帧字段为image_url和tail_image_url
                    tail_image_url: params.end_image_url,
                    // 这个字段是minimax reference模型的奇葩要求，没有的话会报422扣款
                    subject_reference_image_url: params.refer_img_url,
                    enable_safety_checker: false
                },
                webhookUrl: this.webhookUrl
            });

            return { request_id: response.request_id };
        } catch (error) {
            this.logger.error(`Error calling fal.ai image-to-video API: ${error.message}`, error.stack);
            throw error;
        }
    }

    /**
     * 获取任务状态
     * @param requestId 请求ID
     * @param modelId 模型ID
     * @returns 任务状态
     */
    async getTaskStatus(requestId: string, modelId: string): Promise<any> {
        try {
            // 使用fal.queue.status获取任务状态
            const status = await fal.queue.status(modelId, {
                requestId: requestId,
                logs: true
            });

            return status;
        } catch (error) {
            this.logger.error(`Error getting task status from fal.ai: ${error.message}`, error.stack);
            throw error;
        }
    }

    /**
     * 获取任务结果
     * @param requestId 请求ID
     * @param modelId 模型ID
     * @returns 任务结果
     */
    async getTaskResult(requestId: string, modelId: string): Promise<any> {
        try {
            // 使用fal.queue.result获取任务结果
            const result = await fal.queue.result(modelId, {
                requestId: requestId
            });

            return result;
        } catch (error) {
            this.logger.error(`Error getting task result from fal.ai: ${error.message}`, error.stack);
            throw error;
        }
    }

    /**
     * 调用fal.ai的图片生成API
     * @param model 模型名称
     * @param params 请求参数
     * @returns 响应结果
     */
    async generateImage(model: string, params: GenPicRequestDto): Promise<any> {
        try {
            // 根据模型名称确定fal.ai的模型ID
            let falModelId: string;
            switch (model) {
                case 'FLUX Kontext Pro':
                    falModelId = 'fal-ai/flux-pro/kontext';
                    break;
                case 'FLUX Kontext Max':
                    falModelId = 'fal-ai/flux-pro/kontext/max';
                    break;
                default:
                    throw new Error(`Unsupported model: ${model}`);
            }

            // 文生图的情况
            if (!params.image_url && !params.image_urls) {
                falModelId += '/text-to-image';
            } else if (params.image_urls) {
                // 支持2张图
                falModelId += '/multi';
            }

            const input: any = {
                prompt: params.prompt,
                guidance_scale: params.guidance_scale || 3.5,
                seed: params.seed ? parseInt(params.seed) : undefined,
                aspect_ratio: params.aspect_ratio,
                num_images: 1,
                safety_tolerance: 5, // 最宽松
            };

            if (params.image_url) {
                input.image_url = params.image_url;
            }

            if (params.image_urls) {
                input.image_urls = params.image_urls;
            }

            const { request_id } = await fal.queue.submit(falModelId, {
                input,
                webhookUrl: this.picWebhookUrl
            });

            return { request_id };
        } catch (error) {
            this.logger.error(`Error calling fal.ai image generation API: ${error.message}`, error.stack);
            throw error;
        }
    }
}
